# Netlify Deployment Guide for Vue.js Quiz App

This guide provides step-by-step instructions for deploying the Vue.js Quiz Application to Netlify with proper SPA routing support.

## 🚀 Quick Deployment

### Method 1: Git Integration (Recommended)

1. **Push your code to GitHub/GitLab/Bitbucket**
   ```bash
   git add .
   git commit -m "Add Netlify configuration"
   git push origin main
   ```

2. **Connect to Netlify**
   - Go to [Netlify](https://netlify.com)
   - Click "New site from Git"
   - Choose your repository
   - Configure build settings:
     - **Build command**: `npm run build`
     - **Publish directory**: `dist`
     - **Node version**: `18` (in Environment variables)

3. **Deploy**
   - Click "Deploy site"
   - Netlify will automatically build and deploy your app

### Method 2: Manual Deployment

1. **Build the application locally**
   ```bash
   npm run build
   ```

2. **Deploy to Netlify**
   - Go to [Netlify](https://netlify.com)
   - Drag and drop the `dist` folder to the deploy area

## 🔧 Configuration Files

The following files have been added to fix SPA routing:

### `public/_redirects`
```
/*    /index.html   200
```
This tells <PERSON>lify to serve `index.html` for all routes with a 200 status code.

### `netlify.toml`
Comprehensive configuration including:
- Build settings
- Redirect rules
- Security headers
- Caching policies
- Performance optimizations

## 🧪 Testing Your Deployment

### Automated Testing
Use the provided script to test all routes:

```bash
# After deployment, test your live site
node scripts/test-routes.js https://your-app.netlify.app
```

### Manual Testing
Test these URLs directly in your browser:

1. **Root**: `https://your-app.netlify.app/`
2. **Topics**: `https://your-app.netlify.app/topics`
3. **Quiz**: `https://your-app.netlify.app/topic/Fundamentals%20%26%20Core%20Concepts`
4. **Results**: `https://your-app.netlify.app/results`

Each should load the Vue app without 404 errors.

## 🔍 Troubleshooting

### Common Issues and Solutions

#### Issue: 404 on page refresh
**Symptoms**: Direct URLs or page refreshes show "Page not found"
**Solution**: Ensure `_redirects` file is in the `public` directory

#### Issue: Build fails
**Symptoms**: Deployment fails during build process
**Solutions**:
1. Check Node.js version (should be 16+)
2. Verify all dependencies are in `package.json`
3. Check build logs for specific errors

#### Issue: Routes work but content is wrong
**Symptoms**: Pages load but show incorrect content
**Solution**: Clear browser cache and check Vue Router configuration

#### Issue: Assets not loading
**Symptoms**: CSS/JS files return 404
**Solution**: Check `base` configuration in `vite.config.js`

### Debug Steps

1. **Check Netlify Function Logs**
   - Go to your Netlify dashboard
   - Click on your site
   - Go to "Functions" tab
   - Check logs for errors

2. **Verify Build Output**
   - Check that `dist` folder contains `index.html`
   - Verify `_redirects` file is copied to `dist`

3. **Test Locally**
   ```bash
   npm run build
   npm run preview
   ```

## 🌐 Custom Domain Setup

### Adding a Custom Domain

1. **In Netlify Dashboard**
   - Go to Site settings → Domain management
   - Click "Add custom domain"
   - Enter your domain name

2. **Configure DNS**
   - Point your domain to Netlify's servers
   - Add CNAME record: `your-domain.com` → `your-app.netlify.app`

3. **Enable HTTPS**
   - Netlify automatically provides SSL certificates
   - Force HTTPS in Site settings → HTTPS

## 📊 Performance Optimization

### Netlify-Specific Optimizations

1. **Asset Optimization**
   - Enable asset optimization in Site settings
   - Compress images automatically
   - Minify CSS and JavaScript

2. **CDN Configuration**
   - Netlify automatically uses their global CDN
   - Configure cache headers in `netlify.toml`

3. **Build Optimization**
   ```bash
   # Use build plugins for optimization
   npm install --save-dev @netlify/plugin-lighthouse
   ```

## 🔒 Security Configuration

### Headers Configuration
The `netlify.toml` includes security headers:
- `X-Frame-Options: DENY`
- `X-Content-Type-Options: nosniff`
- `X-XSS-Protection: 1; mode=block`
- `Referrer-Policy: strict-origin-when-cross-origin`

### Environment Variables
Set environment variables in Netlify dashboard:
- Go to Site settings → Environment variables
- Add production-specific variables

## 📱 Branch Deployments

### Preview Deployments
- Every pull request gets a preview deployment
- Test changes before merging to main
- Preview URLs: `deploy-preview-123--your-app.netlify.app`

### Branch Deployments
- Deploy specific branches for staging
- Configure in Site settings → Build & deploy

## 🚨 Monitoring and Alerts

### Set Up Notifications
1. **Deploy Notifications**
   - Site settings → Build & deploy → Deploy notifications
   - Add Slack, email, or webhook notifications

2. **Uptime Monitoring**
   - Use external services like UptimeRobot
   - Monitor key routes for availability

### Analytics
- Enable Netlify Analytics in Site settings
- Track page views and performance metrics

## 📋 Deployment Checklist

Before deploying to production:

- [ ] All routes tested locally
- [ ] `_redirects` file in `public` directory
- [ ] Build completes without errors
- [ ] Environment variables configured
- [ ] Custom domain configured (if applicable)
- [ ] HTTPS enabled
- [ ] Security headers configured
- [ ] Performance optimizations enabled
- [ ] Monitoring and alerts set up

## 🆘 Support

If you encounter issues:

1. Check [Netlify Documentation](https://docs.netlify.com/)
2. Review [Vue Router Documentation](https://router.vuejs.org/)
3. Check the [Netlify Community Forum](https://community.netlify.com/)
4. Use the route testing script for debugging

## 📚 Additional Resources

- [Netlify SPA Documentation](https://docs.netlify.com/routing/redirects/rewrites-proxies/#history-pushstate-and-single-page-apps)
- [Vue.js Deployment Guide](https://vuejs.org/guide/best-practices/production-deployment.html)
- [Vite Deployment Guide](https://vitejs.dev/guide/static-deploy.html#netlify)
