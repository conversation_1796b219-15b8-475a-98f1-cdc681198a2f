# Parallel Processing Quiz App

An interactive Vue.js application for testing knowledge of parallel processing concepts, architectures, and performance metrics. Features a modern dark/light mode interface with comprehensive accessibility support.

## ✨ Features

- **Interactive Quiz System**: Multiple choice and true/false questions
- **Dark/Light Mode**: Automatic theme detection with manual toggle
- **Accessibility First**: WCAG compliant with keyboard navigation, ARIA labels, and screen reader support
- **Responsive Design**: Works seamlessly across desktop, tablet, and mobile devices
- **Performance Optimized**: Code splitting, lazy loading, and optimized bundle sizes
- **Error Handling**: Comprehensive error boundaries and user feedback
- **Progress Tracking**: Real-time quiz progress and performance metrics

## 🚀 Quick Start

### Prerequisites

- Node.js 16+ or Bun
- Modern web browser with ES2015+ support

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd pp-quiz-app

# Install dependencies
npm install
# or
bun install
```

### Development

```bash
# Start development server
npm run dev
# or
bun dev

# Open http://localhost:3000 in your browser
```

### Production Build

```bash
# Build for production
npm run build
# or
bun run build

# Preview production build
npm run preview
# or
bun preview
```

## 🏗️ Architecture

### Tech Stack

- **Frontend**: Vue 3 (Composition API)
- **Styling**: Tailwind CSS with custom design system
- **State Management**: Pinia
- **Routing**: Vue Router with lazy loading
- **Animations**: GSAP
- **Build Tool**: Vite with optimized configuration
- **Performance**: Custom monitoring and Core Web Vitals tracking

### Project Structure

```
src/
├── assets/          # Stylesheets and static assets
├── components/      # Reusable Vue components
│   ├── ErrorBoundary.vue
│   ├── LoadingSpinner.vue
│   ├── SkipToContent.vue
│   └── ThemeToggle.vue
├── stores/          # Pinia stores
│   ├── quiz.js      # Quiz state management
│   └── theme.js     # Theme state management
├── utils/           # Utility functions
│   └── performance.js
├── views/           # Page components
│   ├── SplashView.vue
│   ├── TopicsView.vue
│   ├── TopicView.vue
│   └── ResultsView.vue
├── router/          # Vue Router configuration
└── main.js          # Application entry point
```

## 🎨 Design System

### Color Palette

- **Primary**: Indigo (accessibility compliant)
- **Secondary**: Emerald
- **Accent**: Amber
- **Background**: Custom dark blue theme
- **Text**: Semantic color tokens for optimal contrast

### Typography

- System font stack for optimal performance
- Responsive typography scales
- Proper heading hierarchy

## ♿ Accessibility Features

- **WCAG 2.1 AA Compliant**: Meets accessibility standards
- **Keyboard Navigation**: Full keyboard support for all interactions
- **Screen Reader Support**: Proper ARIA labels and semantic HTML
- **Color Contrast**: 4.5:1 minimum contrast ratio
- **Motion Preferences**: Respects `prefers-reduced-motion`
- **Focus Management**: Visible focus indicators and logical tab order

## 📱 Responsive Design

- **Mobile First**: Optimized for mobile devices
- **Breakpoints**: Tailwind's responsive system
- **Touch Friendly**: Appropriate touch targets (44px minimum)
- **Flexible Layouts**: CSS Grid and Flexbox

## ⚡ Performance Optimizations

### Bundle Optimization

- **Code Splitting**: Automatic route-based splitting
- **Tree Shaking**: Unused code elimination
- **Vendor Chunks**: Separate chunks for libraries
- **Asset Optimization**: Optimized images and fonts

### Runtime Performance

- **Lazy Loading**: Components and routes loaded on demand
- **Memory Management**: Proper cleanup and leak prevention
- **Core Web Vitals**: LCP, FID, and CLS monitoring
- **Caching Strategy**: Efficient browser caching

## 🔧 Configuration

### Environment Variables

Create a `.env` file for environment-specific configuration:

```env
VITE_APP_TITLE=Parallel Processing Quiz App
VITE_API_URL=your-api-url
```

### Tailwind Configuration

The app uses a custom Tailwind configuration with:

- Extended color palette
- Custom animations
- Dark mode support
- Accessibility-focused utilities

## 🧪 Testing

### Manual Testing Checklist

- [ ] All quiz flows work correctly
- [ ] Dark/light mode toggle functions
- [ ] Keyboard navigation works
- [ ] Screen reader compatibility
- [ ] Mobile responsiveness
- [ ] Error handling
- [ ] Performance metrics

### Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 🚀 Deployment

### Static Hosting (Recommended)

```bash
# Build the application
npm run build

# Deploy the dist/ folder to your hosting provider
```

**Supported Platforms:**

- Vercel
- Netlify
- GitHub Pages
- AWS S3 + CloudFront
- Firebase Hosting

### Server Configuration

For SPA routing, configure your server to serve `index.html` for all routes:

**Nginx:**

```nginx
location / {
  try_files $uri $uri/ /index.html;
}
```

**Apache:**

```apache
RewriteEngine On
RewriteRule ^(?!.*\.).*$ /index.html [L]
```

## 📊 Performance Metrics

The application includes built-in performance monitoring:

- **Bundle Size**: ~250KB gzipped
- **First Contentful Paint**: <1.5s
- **Largest Contentful Paint**: <2.5s
- **First Input Delay**: <100ms
- **Cumulative Layout Shift**: <0.1

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For issues and questions:

1. Check the existing issues
2. Create a new issue with detailed information
3. Include browser version and steps to reproduce
