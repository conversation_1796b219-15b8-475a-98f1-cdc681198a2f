// Performance monitoring utilities

export class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.isEnabled = import.meta.env.DEV;
  }

  // Start timing an operation
  startTiming(label) {
    if (!this.isEnabled) return;
    
    this.metrics.set(label, {
      startTime: performance.now(),
      startMark: `${label}-start`
    });
    
    if (performance.mark) {
      performance.mark(`${label}-start`);
    }
  }

  // End timing an operation
  endTiming(label) {
    if (!this.isEnabled) return;
    
    const metric = this.metrics.get(label);
    if (!metric) {
      console.warn(`No timing started for label: ${label}`);
      return;
    }

    const endTime = performance.now();
    const duration = endTime - metric.startTime;
    
    if (performance.mark && performance.measure) {
      performance.mark(`${label}-end`);
      performance.measure(label, `${label}-start`, `${label}-end`);
    }

    console.log(`⏱️ ${label}: ${duration.toFixed(2)}ms`);
    
    // Store the result
    this.metrics.set(label, {
      ...metric,
      endTime,
      duration
    });

    return duration;
  }

  // Get all metrics
  getMetrics() {
    if (!this.isEnabled) return {};
    
    const result = {};
    for (const [label, metric] of this.metrics) {
      if (metric.duration !== undefined) {
        result[label] = metric.duration;
      }
    }
    return result;
  }

  // Log performance summary
  logSummary() {
    if (!this.isEnabled) return;
    
    const metrics = this.getMetrics();
    if (Object.keys(metrics).length === 0) {
      console.log('📊 No performance metrics recorded');
      return;
    }

    console.group('📊 Performance Summary');
    Object.entries(metrics).forEach(([label, duration]) => {
      const status = duration < 100 ? '🟢' : duration < 500 ? '🟡' : '🔴';
      console.log(`${status} ${label}: ${duration.toFixed(2)}ms`);
    });
    console.groupEnd();
  }

  // Monitor Core Web Vitals
  monitorWebVitals() {
    if (!this.isEnabled || typeof window === 'undefined') return;

    // Largest Contentful Paint
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          console.log(`🎨 LCP: ${lastEntry.startTime.toFixed(2)}ms`);
        });
        observer.observe({ entryTypes: ['largest-contentful-paint'] });
      } catch (e) {
        console.warn('LCP monitoring not supported');
      }

      // First Input Delay
      try {
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            console.log(`⚡ FID: ${entry.processingStart - entry.startTime}ms`);
          });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
      } catch (e) {
        console.warn('FID monitoring not supported');
      }
    }

    // Cumulative Layout Shift
    if ('PerformanceObserver' in window) {
      try {
        let clsValue = 0;
        const clsObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          }
          console.log(`📐 CLS: ${clsValue.toFixed(4)}`);
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
      } catch (e) {
        console.warn('CLS monitoring not supported');
      }
    }
  }

  // Monitor memory usage
  monitorMemory() {
    if (!this.isEnabled || !performance.memory) return;

    const memory = performance.memory;
    console.group('🧠 Memory Usage');
    console.log(`Used: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`Total: ${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`Limit: ${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`);
    console.groupEnd();
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

// Utility functions
export const measureAsync = async (label, asyncFn) => {
  performanceMonitor.startTiming(label);
  try {
    const result = await asyncFn();
    performanceMonitor.endTiming(label);
    return result;
  } catch (error) {
    performanceMonitor.endTiming(label);
    throw error;
  }
};

export const measureSync = (label, syncFn) => {
  performanceMonitor.startTiming(label);
  try {
    const result = syncFn();
    performanceMonitor.endTiming(label);
    return result;
  } catch (error) {
    performanceMonitor.endTiming(label);
    throw error;
  }
};

// Initialize monitoring when module loads
if (typeof window !== 'undefined') {
  performanceMonitor.monitorWebVitals();
  
  // Log memory usage periodically in development
  if (import.meta.env.DEV) {
    setInterval(() => {
      performanceMonitor.monitorMemory();
    }, 30000); // Every 30 seconds
  }
}
