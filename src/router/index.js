import { createRouter, createWebHistory } from "vue-router";

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: "/",
      name: "splash",
      component: () => import("../views/SplashView.vue"),
    },
    {
      path: "/topics",
      name: "topics",
      component: () => import("../views/TopicsView.vue"),
    },
    {
      path: "/topic/:id",
      name: "topic",
      component: () => import("../views/TopicView.vue"),
      props: true,
    },
    {
      path: "/results",
      name: "results",
      component: () => import("../views/ResultsView.vue"),
    },
  ],
});

export default router;
