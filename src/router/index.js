import { createRouter, createWebHistory } from "vue-router";

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: "/",
      name: "splash",
      component: () => import("../views/SplashView.vue"),
      meta: {
        title: "Welcome - Parallel Processing Quiz",
        description:
          "Welcome to the interactive parallel processing quiz application",
      },
    },
    {
      path: "/topics",
      name: "topics",
      component: () => import("../views/TopicsView.vue"),
      meta: {
        title: "Topics - Parallel Processing Quiz",
        description: "Choose a topic to start your parallel processing quiz",
      },
    },
    {
      path: "/topic/:id",
      name: "topic",
      component: () => import("../views/TopicView.vue"),
      props: true,
      meta: {
        title: "Quiz - Parallel Processing",
        description: "Take the parallel processing quiz",
      },
    },
    {
      path: "/results",
      name: "results",
      component: () => import("../views/ResultsView.vue"),
      meta: {
        title: "Results - Parallel Processing Quiz",
        description: "View your quiz results and performance",
      },
    },
    {
      path: "/loading",
      name: "loading",
      component: () => import("../components/LoadingSpinner.vue"),
      meta: {
        title: "Loading...",
        description: "Loading application",
      },
    },
    // Catch-all route for 404s
    {
      path: "/:pathMatch(.*)*",
      name: "not-found",
      redirect: "/",
    },
  ],
});

// Global navigation guards for meta tags
router.beforeEach((to, from, next) => {
  // Update document title
  if (to.meta.title) {
    document.title = to.meta.title;
  }

  // Update meta description
  if (to.meta.description) {
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute("content", to.meta.description);
    }
  }

  next();
});

export default router;
