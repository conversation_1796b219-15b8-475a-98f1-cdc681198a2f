<template>
  <button
    @click="toggleTheme"
    @keydown.enter="toggleTheme"
    @keydown.space.prevent="toggleTheme"
    class="theme-toggle-btn"
    :aria-label="isDark ? 'Switch to light mode' : 'Switch to dark mode'"
    :title="isDark ? 'Switch to light mode' : 'Switch to dark mode'"
    role="switch"
    :aria-checked="isDark"
    tabindex="0"
  >
    <div class="theme-toggle-container">
      <!-- Sun Icon -->
      <svg
        v-show="!isDark"
        class="theme-icon sun-icon"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
        ></path>
      </svg>

      <!-- <PERSON> Icon -->
      <svg
        v-show="isDark"
        class="theme-icon moon-icon"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
        ></path>
      </svg>
    </div>
  </button>
</template>

<script setup>
import { computed } from "vue";
import { useThemeStore } from "../stores/theme";

const themeStore = useThemeStore();

const isDark = computed(() => themeStore.isDark);

const toggleTheme = () => {
  themeStore.toggleTheme();
};
</script>

<style scoped>
.theme-toggle-btn {
  @apply relative p-2 rounded-lg transition-all duration-300 ease-in-out;
  @apply bg-surface dark:bg-surface-dark;
  @apply border border-border dark:border-border-dark;
  @apply text-text-secondary dark:text-text-dark-secondary;
  @apply hover:bg-surface-secondary dark:hover:bg-surface-dark-secondary;
  @apply hover:text-text-primary dark:hover:text-text-dark-primary;
  @apply hover:border-border-secondary dark:hover:border-border-dark-secondary;
  @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2;
  @apply dark:focus:ring-primary-dark dark:focus:ring-offset-background-dark;
  @apply shadow-sm hover:shadow-md dark:shadow-dark-sm dark:hover:shadow-dark-md;
  @apply transform hover:scale-105 active:scale-95;
}

.theme-toggle-container {
  @apply relative w-6 h-6 flex items-center justify-center;
}

.theme-icon {
  @apply w-5 h-5 transition-all duration-300 ease-in-out;
  @apply absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2;
}

.sun-icon {
  @apply text-amber-500 animate-fade-in;
  animation: rotate 0.3s ease-in-out;
}

.moon-icon {
  @apply text-indigo-400 animate-fade-in;
  animation: rotate 0.3s ease-in-out;
}

@keyframes rotate {
  from {
    transform: translate(-50%, -50%) rotate(-90deg) scale(0.8);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -50%) rotate(0deg) scale(1);
    opacity: 1;
  }
}

/* Hover effects */
.theme-toggle-btn:hover .sun-icon {
  @apply text-amber-400;
}

.theme-toggle-btn:hover .moon-icon {
  @apply text-indigo-300;
}

/* Focus styles for accessibility */
.theme-toggle-btn:focus-visible {
  @apply ring-2 ring-primary ring-offset-2;
  @apply dark:ring-primary-dark dark:ring-offset-background-dark;
}
</style>
