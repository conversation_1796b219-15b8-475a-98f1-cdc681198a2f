<template>
  <div v-if="hasError" class="error-boundary">
    <div class="error-container">
      <div class="error-icon">
        <svg class="w-16 h-16 text-red-500 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
      </div>
      
      <h2 class="error-title">
        Oops! Something went wrong
      </h2>
      
      <p class="error-message">
        {{ errorMessage || 'An unexpected error occurred while loading the application.' }}
      </p>
      
      <div class="error-actions">
        <button 
          class="btn btn-primary"
          @click="retry"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          Try Again
        </button>
        
        <button 
          class="btn btn-ghost"
          @click="goHome"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
          </svg>
          Go Home
        </button>
      </div>
      
      <details v-if="isDevelopment && errorDetails" class="error-details">
        <summary class="error-details-summary">
          Technical Details (Development Mode)
        </summary>
        <pre class="error-details-content">{{ errorDetails }}</pre>
      </details>
    </div>
  </div>
  
  <slot v-else />
</template>

<script setup>
import { ref, onErrorCaptured } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

const hasError = ref(false);
const errorMessage = ref('');
const errorDetails = ref('');
const isDevelopment = import.meta.env.DEV;

onErrorCaptured((error, instance, info) => {
  console.error('Error caught by ErrorBoundary:', error);
  
  hasError.value = true;
  errorMessage.value = error.message || 'An unexpected error occurred';
  
  if (isDevelopment) {
    errorDetails.value = `
Error: ${error.message}
Stack: ${error.stack}
Component: ${instance?.$options.name || 'Unknown'}
Info: ${info}
    `.trim();
  }
  
  return false; // Prevent the error from propagating further
});

const retry = () => {
  hasError.value = false;
  errorMessage.value = '';
  errorDetails.value = '';
  
  // Force a re-render by reloading the current route
  const currentRoute = router.currentRoute.value;
  router.replace({ path: '/loading' }).then(() => {
    router.replace(currentRoute);
  });
};

const goHome = () => {
  hasError.value = false;
  errorMessage.value = '';
  errorDetails.value = '';
  router.push('/');
};
</script>

<style scoped>
.error-boundary {
  @apply min-h-screen flex items-center justify-center p-4;
  @apply bg-background dark:bg-background-dark;
}

.error-container {
  @apply max-w-md w-full text-center space-y-6;
}

.error-icon {
  @apply flex justify-center;
}

.error-title {
  @apply text-2xl font-bold text-heading;
}

.error-message {
  @apply text-body leading-relaxed;
}

.error-actions {
  @apply flex flex-col sm:flex-row gap-3 justify-center;
}

.error-details {
  @apply mt-8 text-left;
}

.error-details-summary {
  @apply cursor-pointer text-sm font-medium text-body hover:text-primary dark:hover:text-primary-dark;
  @apply border border-border dark:border-border-dark rounded-lg p-3;
  @apply bg-surface-secondary dark:bg-surface-dark-secondary;
}

.error-details-content {
  @apply mt-2 p-4 text-xs font-mono;
  @apply bg-surface-secondary dark:bg-surface-dark-secondary;
  @apply border border-border dark:border-border-dark rounded-lg;
  @apply text-text-secondary dark:text-text-dark-secondary;
  @apply overflow-auto max-h-64;
}
</style>
