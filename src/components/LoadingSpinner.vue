<template>
  <div class="loading-spinner" :class="{ 'full-screen': fullScreen }">
    <div class="spinner-container">
      <div class="spinner" :class="sizeClass">
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
      </div>
      <p v-if="message" class="loading-message" :class="textSizeClass">
        {{ message }}
      </p>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  message: {
    type: String,
    default: ''
  },
  fullScreen: {
    type: Boolean,
    default: false
  }
});

const sizeClass = computed(() => {
  const sizes = {
    small: 'w-6 h-6',
    medium: 'w-12 h-12',
    large: 'w-16 h-16'
  };
  return sizes[props.size];
});

const textSizeClass = computed(() => {
  const sizes = {
    small: 'text-sm',
    medium: 'text-base',
    large: 'text-lg'
  };
  return sizes[props.size];
});
</script>

<style scoped>
.loading-spinner {
  @apply flex items-center justify-center;
}

.loading-spinner.full-screen {
  @apply fixed inset-0 bg-background/80 dark:bg-background-dark/80 backdrop-blur-sm z-50;
}

.spinner-container {
  @apply flex flex-col items-center space-y-4;
}

.spinner {
  @apply relative;
}

.spinner-ring {
  @apply absolute border-4 border-transparent rounded-full animate-spin;
  border-top-color: theme('colors.primary.DEFAULT');
  animation-duration: 1.2s;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
}

.dark .spinner-ring {
  border-top-color: theme('colors.primary.dark');
}

.spinner-ring:nth-child(1) {
  @apply w-full h-full;
  animation-delay: 0s;
}

.spinner-ring:nth-child(2) {
  @apply w-5/6 h-5/6 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2;
  animation-delay: -0.3s;
  border-top-color: theme('colors.secondary.DEFAULT');
}

.dark .spinner-ring:nth-child(2) {
  border-top-color: theme('colors.secondary.dark');
}

.spinner-ring:nth-child(3) {
  @apply w-2/3 h-2/3 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2;
  animation-delay: -0.6s;
  border-top-color: theme('colors.accent.DEFAULT');
}

.dark .spinner-ring:nth-child(3) {
  border-top-color: theme('colors.accent.dark');
}

.spinner-ring:nth-child(4) {
  @apply w-1/2 h-1/2 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2;
  animation-delay: -0.9s;
  border-top-color: theme('colors.primary.600');
}

.dark .spinner-ring:nth-child(4) {
  border-top-color: theme('colors.primary.300');
}

.loading-message {
  @apply text-body font-medium animate-pulse;
}

/* Accessibility: Respect prefers-reduced-motion */
@media (prefers-reduced-motion: reduce) {
  .spinner-ring {
    animation: none;
  }
  
  .loading-message {
    animation: none;
  }
}
</style>
