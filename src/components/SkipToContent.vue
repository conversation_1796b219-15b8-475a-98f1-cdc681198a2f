<template>
  <a
    href="#main-content"
    class="skip-to-content"
    @click="focusMainContent"
  >
    Skip to main content
  </a>
</template>

<script setup>
const focusMainContent = () => {
  // Small delay to ensure the element is rendered
  setTimeout(() => {
    const mainContent = document.getElementById('main-content');
    if (mainContent) {
      mainContent.focus();
    }
  }, 100);
};
</script>

<style scoped>
.skip-to-content {
  @apply absolute top-0 left-0 z-50;
  @apply px-4 py-2 bg-primary text-white;
  @apply font-medium text-sm rounded-br-lg;
  @apply transform -translate-y-full;
  @apply transition-transform duration-200 ease-in-out;
  @apply focus:translate-y-0;
  @apply dark:bg-primary-dark;
}

.skip-to-content:focus {
  @apply outline-none ring-2 ring-white ring-offset-2;
  @apply dark:ring-gray-900 dark:ring-offset-background-dark;
}
</style>
