<template>
  <div class="item">
    <i>
      <slot name="icon"></slot>
    </i>
    <div class="details">
      <h3>
        <slot name="heading"></slot>
      </h3>
      <slot></slot>
    </div>
  </div>
</template>

<style scoped>
.item {
  @apply mt-8 flex relative;
}

.details {
  @apply flex-1 ml-4;
}

i {
  @apply flex items-center justify-center w-8 h-8;
  @apply text-text-secondary dark:text-text-dark-secondary;
}

h3 {
  @apply text-lg font-medium mb-2;
  @apply text-text-primary dark:text-text-dark-primary;
}

@media (min-width: 1024px) {
  .item {
    @apply mt-0 py-2 pl-32;
  }

  i {
    @apply absolute top-1/2 -left-6 transform -translate-y-1/2;
    @apply border border-border dark:border-border-dark;
    @apply bg-surface dark:bg-surface-dark;
    @apply rounded-lg w-12 h-12;
    @apply shadow-sm dark:shadow-dark-sm;
  }

  .item:before {
    content: " ";
    @apply absolute left-0 border-l border-border dark:border-border-dark;
    bottom: calc(50% + 24px);
    height: calc(50% - 24px);
  }

  .item:after {
    content: " ";
    @apply absolute left-0 border-l border-border dark:border-border-dark;
    top: calc(50% + 24px);
    height: calc(50% - 24px);
  }

  .item:first-of-type:before {
    @apply hidden;
  }

  .item:last-of-type:after {
    @apply hidden;
  }
}
</style>
