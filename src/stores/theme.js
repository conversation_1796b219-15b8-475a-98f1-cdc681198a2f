import { defineStore } from "pinia";
import { ref, watch } from "vue";

export const useThemeStore = defineStore("theme", () => {
  // State
  const isDark = ref(false);
  const isInitialized = ref(false);

  // Initialize theme from localStorage or system preference
  const initializeTheme = () => {
    if (isInitialized.value) return;

    // Check localStorage first
    const savedTheme = localStorage.getItem("theme");
    if (savedTheme) {
      isDark.value = savedTheme === "dark";
    } else {
      // Fall back to system preference
      isDark.value = window.matchMedia("(prefers-color-scheme: dark)").matches;
    }

    // Apply theme to document
    applyTheme();
    isInitialized.value = true;

    // Listen for system theme changes
    window
      .matchMedia("(prefers-color-scheme: dark)")
      .addEventListener("change", (e) => {
        if (!localStorage.getItem("theme")) {
          isDark.value = e.matches;
        }
      });
  };

  // Apply theme to document
  const applyTheme = () => {
    const html = document.documentElement;
    if (isDark.value) {
      html.classList.add("dark");
    } else {
      html.classList.remove("dark");
    }
  };

  // Toggle theme
  const toggleTheme = () => {
    isDark.value = !isDark.value;
  };

  // Set specific theme
  const setTheme = (theme) => {
    isDark.value = theme === "dark";
  };

  // Watch for theme changes and persist to localStorage
  watch(
    isDark,
    (newValue) => {
      localStorage.setItem("theme", newValue ? "dark" : "light");
      applyTheme();
    },
    { immediate: false }
  );

  // Getters
  const currentTheme = () => (isDark.value ? "dark" : "light");
  const isLightMode = () => !isDark.value;
  const isDarkMode = () => isDark.value;

  return {
    // State
    isDark,
    isInitialized,
    
    // Actions
    initializeTheme,
    toggleTheme,
    setTheme,
    applyTheme,
    
    // Getters
    currentTheme,
    isLightMode,
    isDarkMode,
  };
});
