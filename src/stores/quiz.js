import { defineStore } from "pinia";
import quizData from "../../quiz-data.json";

export const useQuizStore = defineStore("quiz", {
  state: () => ({
    topics: quizData.quiz,
    currentTopic: null,
    currentQuestionIndex: 0,
    answers: {},
    startTime: null,
    endTime: null,
    showHint: false,
  }),

  getters: {
    currentQuestion: (state) => {
      if (!state.currentTopic) return null;
      const questions = state.currentTopic.questions;
      return questions[state.currentQuestionIndex];
    },

    progress: (state) => {
      if (!state.currentTopic) return 0;
      const total = state.currentTopic.questions.length;
      const answered = Object.keys(state.answers).length;
      return (answered / total) * 100;
    },

    score: (state) => {
      let correct = 0;
      let total = 0;

      Object.entries(state.answers).forEach(([questionId, answer]) => {
        const question = state.currentTopic.questions.find(
          (q) => q.question === questionId
        );
        if (question && answer === question.answer) {
          correct++;
        }
        total++;
      });

      return total > 0 ? (correct / total) * 100 : 0;
    },
  },

  actions: {
    setCurrentTopic(topicId) {
      this.currentTopic = this.topics.find((t) => t.topic === topicId);
      this.currentQuestionIndex = 0;
      this.answers = {};
      this.startTime = Date.now();
      this.showHint = false;
    },

    submitAnswer(questionId, answer) {
      this.answers[questionId] = answer;
    },

    nextQuestion() {
      if (this.currentQuestionIndex < this.currentTopic.questions.length - 1) {
        this.currentQuestionIndex++;
        this.showHint = false;
      }
    },

    previousQuestion() {
      if (this.currentQuestionIndex > 0) {
        this.currentQuestionIndex--;
        this.showHint = false;
      }
    },

    toggleHint() {
      this.showHint = !this.showHint;
    },

    finishQuiz() {
      this.endTime = Date.now();
    },

    resetQuiz() {
      this.currentTopic = null;
      this.currentQuestionIndex = 0;
      this.answers = {};
      this.startTime = null;
      this.endTime = null;
      this.showHint = false;
    },
  },
});
