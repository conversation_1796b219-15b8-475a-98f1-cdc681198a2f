import "./assets/main.css";

import { createApp } from "vue";
import { createPinia } from "pinia";
import App from "./App.vue";
import router from "./router";
import { performanceMonitor, measureSync } from "./utils/performance";

// Start app initialization timing
performanceMonitor.startTiming("app-initialization");

const app = measureSync("create-app", () => createApp(App));

app.use(createPinia());
app.use(router);

// Error handling
app.config.errorHandler = (err, instance, info) => {
  console.error("Global error:", err);
  console.error("Component:", instance);
  console.error("Info:", info);

  // In production, you might want to send this to an error reporting service
  if (import.meta.env.PROD) {
    // Example: sendToErrorReporting(err, instance, info);
  }
};

// Performance warning handler
app.config.warnHandler = (msg, instance, trace) => {
  if (import.meta.env.DEV) {
    console.warn("Vue warning:", msg);
    console.warn("Component:", instance);
    console.warn("Trace:", trace);
  }
};

measureSync("app-mount", () => app.mount("#app"));

performanceMonitor.endTiming("app-initialization");

// Log performance summary after a short delay
setTimeout(() => {
  performanceMonitor.logSummary();
}, 1000);
