@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    @apply antialiased;
  }

  body {
    @apply bg-background text-gray-900 dark:bg-background-dark dark:text-gray-100 min-h-screen transition-colors duration-300;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm;
  }

  .btn-primary {
    @apply bg-primary text-white hover:bg-primary/90 dark:bg-primary-dark dark:hover:bg-primary/80;
  }

  .btn-secondary {
    @apply bg-secondary text-white hover:bg-secondary/90 dark:bg-secondary-dark dark:hover:bg-secondary/80;
  }

  .card {
    @apply bg-surface dark:bg-surface-dark rounded-xl shadow-lg p-6 transition-all duration-200 hover:shadow-xl;
  }

  .topic-card {
    @apply card cursor-pointer hover:scale-105;
  }
}

/* Remove all conflicting styles */
#app {
  @apply w-full min-h-screen;
}
