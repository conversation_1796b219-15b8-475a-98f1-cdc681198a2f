@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    @apply antialiased scroll-smooth;
    transition: color-scheme 0.3s ease-in-out;
  }

  body {
    @apply bg-background dark:bg-background-dark;
    @apply text-text-primary dark:text-text-dark-primary;
    @apply min-h-screen transition-all duration-300 ease-in-out;
    @apply selection:bg-primary/20 dark:selection:bg-primary-dark/30;
  }

  /* Smooth transitions for all elements */
  * {
    @apply transition-colors duration-300 ease-in-out;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-background-secondary dark:bg-background-dark-secondary;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-border-secondary dark:bg-border-dark rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-border dark:bg-border-dark-secondary;
  }
}

@layer components {
  /* Button Components */
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200;
    @apply shadow-sm hover:shadow-md dark:shadow-dark-sm dark:hover:shadow-dark-md;
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
    @apply transform hover:scale-105 active:scale-95;
  }

  .btn-primary {
    @apply bg-primary text-white hover:bg-primary-600;
    @apply dark:bg-primary-dark dark:hover:bg-primary-500;
    @apply focus:ring-primary dark:focus:ring-primary-dark;
    @apply focus:ring-offset-background dark:focus:ring-offset-background-dark;
  }

  .btn-secondary {
    @apply bg-secondary text-white hover:bg-secondary-600;
    @apply dark:bg-secondary-dark dark:hover:bg-secondary-500;
    @apply focus:ring-secondary dark:focus:ring-secondary-dark;
    @apply focus:ring-offset-background dark:focus:ring-offset-background-dark;
  }

  .btn-accent {
    @apply bg-accent text-white hover:bg-accent-600;
    @apply dark:bg-accent-dark dark:hover:bg-accent-500;
    @apply focus:ring-accent dark:focus:ring-accent-dark;
    @apply focus:ring-offset-background dark:focus:ring-offset-background-dark;
  }

  .btn-outline {
    @apply bg-transparent border-2 border-border dark:border-border-dark;
    @apply text-text-primary dark:text-text-dark-primary;
    @apply hover:bg-surface-secondary dark:hover:bg-surface-dark-secondary;
    @apply hover:border-border-secondary dark:hover:border-border-dark-secondary;
  }

  .btn-ghost {
    @apply bg-transparent text-text-secondary dark:text-text-dark-secondary;
    @apply hover:bg-surface-secondary dark:hover:bg-surface-dark-secondary;
    @apply hover:text-text-primary dark:hover:text-text-dark-primary;
  }

  /* Card Components */
  .card {
    @apply bg-surface dark:bg-surface-dark;
    @apply border border-border dark:border-border-dark;
    @apply rounded-xl shadow-lg dark:shadow-dark-lg;
    @apply p-6 transition-all duration-200;
    @apply hover:shadow-xl dark:hover:shadow-dark-xl;
    @apply hover:border-border-secondary dark:hover:border-border-dark-secondary;
  }

  .card-interactive {
    @apply card cursor-pointer;
    @apply hover:scale-105 hover:-translate-y-1;
    @apply active:scale-100 active:translate-y-0;
  }

  .topic-card {
    @apply card-interactive;
  }

  /* Input Components */
  .input {
    @apply w-full px-3 py-2 rounded-lg;
    @apply bg-surface dark:bg-surface-dark;
    @apply border border-border dark:border-border-dark;
    @apply text-text-primary dark:text-text-dark-primary;
    @apply placeholder-text-tertiary dark:placeholder-text-dark-tertiary;
    @apply focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-dark;
    @apply focus:border-transparent transition-all duration-200;
  }

  /* Progress Bar */
  .progress-bar {
    @apply w-full bg-surface-secondary dark:bg-surface-dark-secondary;
    @apply rounded-full h-2.5 overflow-hidden;
  }

  .progress-fill {
    @apply bg-gradient-to-r from-primary to-primary-600;
    @apply dark:from-primary-dark dark:to-primary-500;
    @apply h-full rounded-full transition-all duration-300 ease-out;
  }

  /* Text Styles */
  .text-heading {
    @apply text-text-primary dark:text-text-dark-primary font-bold;
  }

  .text-body {
    @apply text-text-secondary dark:text-text-dark-secondary;
  }

  .text-muted {
    @apply text-text-tertiary dark:text-text-dark-tertiary;
  }
}

/* Remove all conflicting styles */
#app {
  @apply w-full min-h-screen;
}
