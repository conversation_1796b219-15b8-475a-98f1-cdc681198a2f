<script setup>
import { onMounted } from "vue";
import { gsap } from "gsap";
import { useThemeStore } from "./stores/theme";
import ThemeToggle from "./components/ThemeToggle.vue";

const themeStore = useThemeStore();

// Initialize theme on app mount
onMounted(() => {
  themeStore.initializeTheme();
});

const beforeLeave = (el) => {
  gsap.to(el, {
    opacity: 0,
    y: 20,
    duration: 0.3,
  });
};

const enter = (el, done) => {
  gsap.from(el, {
    opacity: 0,
    y: 20,
    duration: 0.3,
    onComplete: done,
  });
};

const afterEnter = (el) => {
  gsap.to(el, {
    opacity: 1,
    y: 0,
    duration: 0.3,
  });
};
</script>

<template>
  <div class="min-h-screen bg-background dark:bg-background-dark relative">
    <!-- Theme Toggle - Fixed position -->
    <div class="fixed top-4 right-4 z-50">
      <ThemeToggle />
    </div>

    <!-- Main Content -->
    <router-view v-slot="{ Component }">
      <transition
        name="page"
        mode="out-in"
        @before-leave="beforeLeave"
        @enter="enter"
        @after-enter="afterEnter"
      >
        <component :is="Component" />
      </transition>
    </router-view>
  </div>
</template>

<style>
.page-enter-active,
.page-leave-active {
  transition: all 0.3s ease;
}

.page-enter-from,
.page-leave-to {
  opacity: 0;
  transform: translateY(20px);
}
</style>
