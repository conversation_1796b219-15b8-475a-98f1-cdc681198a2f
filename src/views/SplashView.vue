<template>
  <div
    class="min-h-screen flex items-center justify-center bg-background dark:bg-background-dark transition-colors duration-300"
  >
    <div class="text-center">
      <h1
        ref="title"
        class="text-4xl md:text-6xl font-bold mb-8 text-gray-900 dark:text-gray-100"
      >
        Welcome to Parallel Processing Quizzes
      </h1>
      <div class="flex justify-center space-x-2">
        <div
          v-for="i in 3"
          :key="i"
          class="w-3 h-3 rounded-full animate-bounce-slow"
          :class="
            i === 1
              ? 'bg-primary dark:bg-primary-dark'
              : i === 2
              ? 'bg-secondary dark:bg-secondary-dark'
              : 'bg-accent dark:bg-accent-dark'
          "
          :style="{ animationDelay: `${(i - 1) * 0.2}s` }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { gsap } from "gsap";

const router = useRouter();
const title = ref(null);

onMounted(() => {
  // Initial animation
  gsap.from(title.value, {
    y: 50,
    opacity: 0,
    duration: 1,
    ease: "power3.out",
  });

  // Navigate to topics after 3 seconds
  setTimeout(() => {
    gsap.to(title.value, {
      y: -50,
      opacity: 0,
      duration: 0.5,
      ease: "power3.in",
      onComplete: () => {
        router.push("/topics");
      },
    });
  }, 3000);
});
</script>
