<template>
  <div
    class="min-h-screen flex items-center justify-center bg-background dark:bg-background-dark transition-all duration-300"
  >
    <div class="text-center px-4">
      <h1
        ref="title"
        class="text-4xl md:text-6xl font-bold mb-8 text-heading bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent dark:from-primary-dark dark:to-secondary-dark"
      >
        Welcome to Parallel Processing Quizzes
      </h1>
      <p class="text-lg md:text-xl text-body mb-12 max-w-2xl mx-auto">
        Test your knowledge of parallel processing concepts with interactive
        quizzes
      </p>
      <div class="flex justify-center space-x-3 mb-8">
        <div
          v-for="i in 3"
          :key="i"
          class="w-4 h-4 rounded-full animate-bounce-slow shadow-lg"
          :class="
            i === 1
              ? 'bg-primary dark:bg-primary-dark'
              : i === 2
              ? 'bg-secondary dark:bg-secondary-dark'
              : 'bg-accent dark:bg-accent-dark'
          "
          :style="{ animationDelay: `${(i - 1) * 0.2}s` }"
        ></div>
      </div>
      <div class="text-muted text-sm">Loading your quiz experience...</div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { gsap } from "gsap";
import LoadingSpinner from "../components/LoadingSpinner.vue";

const router = useRouter();
const title = ref(null);

onMounted(() => {
  // Initial animation
  gsap.from(title.value, {
    y: 50,
    opacity: 0,
    duration: 1,
    ease: "power3.out",
  });

  // Navigate to topics after 3 seconds
  setTimeout(() => {
    gsap.to(title.value, {
      y: -50,
      opacity: 0,
      duration: 0.5,
      ease: "power3.in",
      onComplete: () => {
        router.push("/topics");
      },
    });
  }, 3000);
});
</script>
