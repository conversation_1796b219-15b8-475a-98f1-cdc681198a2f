<template>
  <div class="min-h-screen py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto">
      <h1
        ref="title"
        class="text-3xl md:text-4xl font-bold text-center mb-12 text-gray-800"
      >
        Choose a Topic to Start
      </h1>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div
          v-for="(topic, index) in topics"
          :key="topic.topic"
          class="topic-card bg-white transform transition-all duration-300 hover:scale-105"
          @click="selectTopic(topic.topic)"
        >
          <h2 class="text-xl font-semibold mb-4 text-gray-800">
            {{ topic.topic }}
          </h2>
          <div class="flex justify-between text-sm text-gray-600">
            <span>{{ getQuestionCount(topic, "mcq") }} MCQs</span>
            <span>{{ getQuestionCount(topic, "tf") }} True/False</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useQuizStore } from "../stores/quiz";
import { gsap } from "gsap";

const router = useRouter();
const store = useQuizStore();
const title = ref(null);

const topics = store.topics;

const getQuestionCount = (topic, type) => {
  return topic.questions.filter((q) => q.type === type).length;
};

const selectTopic = (topicId) => {
  store.setCurrentTopic(topicId);
  router.push(`/topic/${encodeURIComponent(topicId)}`);
};

onMounted(() => {
  // Animate title only
  gsap.from(title.value, {
    y: 30,
    opacity: 0,
    duration: 0.8,
    ease: "power3.out",
  });
});
</script>
