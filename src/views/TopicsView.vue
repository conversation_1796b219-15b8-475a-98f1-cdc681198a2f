<template>
  <div
    class="min-h-screen py-12 px-4 sm:px-6 lg:px-8 bg-background dark:bg-background-dark"
  >
    <div class="max-w-7xl mx-auto">
      <div class="text-center mb-12">
        <h1
          ref="title"
          class="text-3xl md:text-4xl font-bold mb-4 text-heading"
        >
          Choose a Topic to Start
        </h1>
        <p class="text-lg text-body max-w-2xl mx-auto">
          Select a topic below to begin your parallel processing quiz journey
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div
          v-for="(topic, index) in topics"
          :key="topic.topic"
          class="topic-card group"
          :tabindex="0"
          role="button"
          :aria-label="`Start quiz on ${topic.topic}`"
          @click="selectTopic(topic.topic)"
          @keydown.enter="selectTopic(topic.topic)"
          @keydown.space.prevent="selectTopic(topic.topic)"
        >
          <div class="flex items-start justify-between mb-4">
            <h2
              class="text-xl font-semibold text-heading group-hover:text-primary dark:group-hover:text-primary-dark transition-colors"
            >
              {{ topic.topic }}
            </h2>
            <div
              class="w-8 h-8 rounded-full bg-primary/10 dark:bg-primary-dark/10 flex items-center justify-center group-hover:bg-primary/20 dark:group-hover:bg-primary-dark/20 transition-colors"
            >
              <svg
                class="w-4 h-4 text-primary dark:text-primary-dark"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5l7 7-7 7"
                ></path>
              </svg>
            </div>
          </div>

          <div class="space-y-3">
            <div class="flex justify-between items-center">
              <span class="text-sm text-body">Multiple Choice</span>
              <span
                class="px-2 py-1 bg-primary/10 dark:bg-primary-dark/10 text-primary dark:text-primary-dark rounded-full text-xs font-medium"
              >
                {{ getQuestionCount(topic, "mcq") }} questions
              </span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm text-body">True/False</span>
              <span
                class="px-2 py-1 bg-secondary/10 dark:bg-secondary-dark/10 text-secondary dark:text-secondary-dark rounded-full text-xs font-medium"
              >
                {{ getQuestionCount(topic, "tf") }} questions
              </span>
            </div>
          </div>

          <div class="mt-4 pt-4 border-t border-border dark:border-border-dark">
            <div class="flex items-center text-xs text-muted">
              <svg
                class="w-3 h-3 mr-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                ></path>
              </svg>
              Estimated time:
              {{
                Math.ceil(
                  (getQuestionCount(topic, "mcq") +
                    getQuestionCount(topic, "tf")) *
                    0.5
                )
              }}
              min
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useQuizStore } from "../stores/quiz";
import { gsap } from "gsap";

const router = useRouter();
const store = useQuizStore();
const title = ref(null);

const topics = store.topics;

const getQuestionCount = (topic, type) => {
  return topic.questions.filter((q) => q.type === type).length;
};

const selectTopic = (topicId) => {
  store.setCurrentTopic(topicId);
  router.push(`/topic/${encodeURIComponent(topicId)}`);
};

onMounted(() => {
  // Animate title only
  gsap.from(title.value, {
    y: 30,
    opacity: 0,
    duration: 0.8,
    ease: "power3.out",
  });
});
</script>
