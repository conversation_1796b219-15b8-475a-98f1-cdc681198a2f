<template>
  <div
    class="min-h-screen py-12 px-4 sm:px-6 lg:px-8 bg-background dark:bg-background-dark"
  >
    <div class="max-w-2xl mx-auto">
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold mb-2 text-heading">Quiz Results</h1>
        <p class="text-body">Here's how you performed on the quiz</p>
      </div>

      <!-- Score Display -->
      <div class="card mb-8 text-center">
        <div class="mb-6">
          <div class="text-6xl font-bold mb-4" :class="scoreClass">
            {{ Math.round(score) }}%
          </div>
          <div class="text-xl text-body mb-4">
            {{ scoreMessage }}
          </div>
          <div class="flex items-center justify-center text-sm text-muted">
            <svg
              class="w-4 h-4 mr-1"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
              ></path>
            </svg>
            Time taken: {{ formatTime(timeTaken) }}
          </div>
        </div>

        <!-- Score breakdown -->
        <div
          class="grid grid-cols-3 gap-4 pt-6 border-t border-border dark:border-border-dark"
        >
          <div class="text-center">
            <div
              class="text-2xl font-bold text-secondary dark:text-secondary-dark"
            >
              {{ correctAnswers }}
            </div>
            <div class="text-xs text-muted">Correct</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-red-500 dark:text-red-400">
              {{ incorrectAnswers }}
            </div>
            <div class="text-xs text-muted">Incorrect</div>
          </div>
          <div class="text-center">
            <div
              class="text-2xl font-bold text-text-secondary dark:text-text-dark-secondary"
            >
              {{ totalQuestions }}
            </div>
            <div class="text-xs text-muted">Total</div>
          </div>
        </div>
      </div>

      <!-- Reaction GIF -->
      <div class="card mb-8 overflow-hidden p-0">
        <img
          :src="reactionGif"
          alt="Reaction"
          class="w-full h-64 object-cover"
        />
      </div>

      <!-- Navigation -->
      <div class="flex justify-center space-x-4">
        <button class="btn btn-primary" @click="retryQuiz">
          <svg
            class="w-4 h-4 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            ></path>
          </svg>
          Retry Quiz
        </button>
        <button class="btn btn-ghost" @click="goToTopics">
          <svg
            class="w-4 h-4 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M10 19l-7-7m0 0l7-7m-7 7h18"
            ></path>
          </svg>
          Back to Topics
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";
import { useRouter } from "vue-router";
import { useQuizStore } from "../stores/quiz";

const router = useRouter();
const store = useQuizStore();

const score = computed(() => store.score);
const timeTaken = computed(() => {
  if (!store.startTime || !store.endTime) return 0;
  return store.endTime - store.startTime;
});

const scoreClass = computed(() => {
  if (score.value >= 80) return "text-secondary dark:text-secondary-dark";
  if (score.value >= 50) return "text-accent dark:text-accent-dark";
  return "text-red-500 dark:text-red-400";
});

const correctAnswers = computed(() => {
  let correct = 0;
  Object.entries(store.answers).forEach(([questionId, answer]) => {
    const question = store.currentTopic.questions.find(
      (q) => q.question === questionId
    );
    if (question && answer === question.answer) {
      correct++;
    }
  });
  return correct;
});

const incorrectAnswers = computed(() => {
  return totalQuestions.value - correctAnswers.value;
});

const totalQuestions = computed(() => {
  return Object.keys(store.answers).length;
});

const scoreMessage = computed(() => {
  if (score.value >= 80) return "Excellent! You really know your stuff! 🎉";
  if (score.value >= 50) return "Good job! Keep practicing! 🙂";
  return "Keep studying! You can do better! 💪";
});

const reactionGif = computed(() => {
  if (score.value >= 80) {
    return "https://media.giphy.com/media/3o7abKhOpu0NwenH3O/giphy.gif"; // Celebration
  }
  if (score.value >= 50) {
    return "https://media.giphy.com/media/3o7TKqnN349PBUtGFO/giphy.gif"; // Thumbs up
  }
  return "https://media.giphy.com/media/3o7TKqnN349PBUtGFO/giphy.gif"; // Encouragement
});

const formatTime = (ms) => {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}m ${remainingSeconds}s`;
};

const retryQuiz = () => {
  store.resetQuiz();
  router.push(`/topic/${encodeURIComponent(store.currentTopic.topic)}`);
};

const goToTopics = () => {
  store.resetQuiz();
  router.push("/topics");
};
</script>
