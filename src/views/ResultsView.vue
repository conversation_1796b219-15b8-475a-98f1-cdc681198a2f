<template>
  <div class="min-h-screen py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-2xl mx-auto">
      <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">
        Quiz Results
      </h1>

      <!-- Score Display -->
      <div class="card bg-white mb-8">
        <div class="text-6xl font-bold mb-4" :class="scoreClass">
          {{ Math.round(score) }}%
        </div>
        <div class="text-xl text-gray-600 mb-4">
          {{ scoreMessage }}
        </div>
        <div class="text-sm text-gray-500">
          Time taken: {{ formatTime(timeTaken) }}
        </div>
      </div>

      <!-- Reaction GIF -->
      <div class="mb-8 bg-white rounded-xl shadow-lg overflow-hidden">
        <img
          :src="reactionGif"
          alt="Reaction"
          class="w-full h-64 object-cover"
        />
      </div>

      <!-- Navigation -->
      <div class="flex justify-center space-x-4">
        <button class="btn btn-primary" @click="retryQuiz">Retry Quiz</button>
        <button
          class="btn bg-gray-200 text-gray-700 hover:bg-gray-300"
          @click="goToTopics"
        >
          Back to Topics
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";
import { useRouter } from "vue-router";
import { useQuizStore } from "../stores/quiz";

const router = useRouter();
const store = useQuizStore();

const score = computed(() => store.score);
const timeTaken = computed(() => {
  if (!store.startTime || !store.endTime) return 0;
  return store.endTime - store.startTime;
});

const scoreClass = computed(() => {
  if (score.value >= 80) return "text-green-500";
  if (score.value >= 50) return "text-yellow-500";
  return "text-red-500";
});

const scoreMessage = computed(() => {
  if (score.value >= 80) return "Excellent! You really know your stuff! 🎉";
  if (score.value >= 50) return "Good job! Keep practicing! 🙂";
  return "Keep studying! You can do better! 💪";
});

const reactionGif = computed(() => {
  if (score.value >= 80) {
    return "https://media.giphy.com/media/3o7abKhOpu0NwenH3O/giphy.gif"; // Celebration
  }
  if (score.value >= 50) {
    return "https://media.giphy.com/media/3o7TKqnN349PBUtGFO/giphy.gif"; // Thumbs up
  }
  return "https://media.giphy.com/media/3o7TKqnN349PBUtGFO/giphy.gif"; // Encouragement
});

const formatTime = (ms) => {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}m ${remainingSeconds}s`;
};

const retryQuiz = () => {
  store.resetQuiz();
  router.push(`/topic/${encodeURIComponent(store.currentTopic.topic)}`);
};

const goToTopics = () => {
  store.resetQuiz();
  router.push("/topics");
};
</script>
