<template>
  <div
    class="min-h-screen py-12 px-4 sm:px-6 lg:px-8 bg-background dark:bg-background-dark"
  >
    <div class="max-w-4xl mx-auto">
      <!-- Header -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold mb-2 text-heading">
          {{ currentTopic?.topic }}
        </h1>
        <p class="text-body">Answer all questions to complete the quiz</p>
      </div>

      <!-- Tabs -->
      <div class="flex space-x-4 mb-8">
        <button
          v-for="tab in ['MCQ', 'True/False']"
          :key="tab"
          class="btn flex-1"
          :class="activeTab === tab ? 'btn-primary' : 'btn-outline'"
          @click="activeTab = tab"
        >
          {{ tab }}
          <span class="ml-2 text-xs opacity-75">
            ({{ filteredQuestions.length }})
          </span>
        </button>
      </div>

      <!-- Progress Bar -->
      <div class="mb-8">
        <div class="flex justify-between items-center mb-2">
          <span class="text-sm text-body">Progress</span>
          <span class="text-sm text-body">{{ Math.round(progress) }}%</span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: `${progress}%` }"></div>
        </div>
      </div>

      <!-- Questions -->
      <div class="space-y-8">
        <div
          v-for="(question, index) in filteredQuestions"
          :key="index"
          class="card"
        >
          <div class="flex items-start justify-between mb-4">
            <h3
              :id="`question-${index}`"
              class="text-lg font-semibold text-heading flex-1"
            >
              {{ question.question }}
            </h3>
            <span
              class="ml-4 px-2 py-1 bg-surface-secondary dark:bg-surface-dark-secondary rounded-full text-xs text-muted"
            >
              {{ index + 1 }} / {{ filteredQuestions.length }}
            </span>
          </div>

          <!-- MCQ Options -->
          <div
            v-if="question.type === 'mcq'"
            class="space-y-3"
            role="radiogroup"
            :aria-labelledby="`question-${index}`"
          >
            <button
              v-for="(option, optionIndex) in question.options"
              :key="option"
              class="w-full text-left p-4 rounded-lg border transition-all group"
              role="radio"
              :aria-checked="answers[question.question] === option"
              :aria-label="`Option ${String.fromCharCode(
                65 + optionIndex
              )}: ${option}`"
              :class="{
                'border-primary dark:border-primary-dark bg-primary/10 dark:bg-primary-dark/10 text-primary dark:text-primary-dark':
                  answers[question.question] === option,
                'border-border dark:border-border-dark hover:border-border-secondary dark:hover:border-border-dark-secondary hover:bg-surface-secondary dark:hover:bg-surface-dark-secondary':
                  answers[question.question] !== option,
              }"
              @click="submitAnswer(question.question, option)"
            >
              <div class="flex items-center">
                <span
                  class="w-6 h-6 rounded-full border-2 mr-3 flex items-center justify-center text-xs font-medium"
                  :class="{
                    'border-primary dark:border-primary-dark bg-primary dark:bg-primary-dark text-white':
                      answers[question.question] === option,
                    'border-border-secondary dark:border-border-dark-secondary group-hover:border-primary dark:group-hover:border-primary-dark':
                      answers[question.question] !== option,
                  }"
                >
                  {{ String.fromCharCode(65 + optionIndex) }}
                </span>
                <span class="flex-1">{{ option }}</span>
              </div>
            </button>
          </div>

          <!-- True/False Options -->
          <div
            v-else
            class="grid grid-cols-2 gap-4"
            role="radiogroup"
            :aria-labelledby="`question-${index}`"
          >
            <button
              v-for="option in [true, false]"
              :key="option"
              class="btn p-6 text-center"
              role="radio"
              :aria-checked="answers[question.question] === option"
              :aria-label="`${option ? 'True' : 'False'} option`"
              :class="{
                'btn-primary': answers[question.question] === option,
                'btn-outline': answers[question.question] !== option,
              }"
              @click="submitAnswer(question.question, option)"
            >
              <div class="flex flex-col items-center">
                <svg
                  class="w-8 h-8 mb-2"
                  :class="
                    option
                      ? 'text-secondary dark:text-secondary-dark'
                      : 'text-red-500 dark:text-red-400'
                  "
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    v-if="option"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M5 13l4 4L19 7"
                  ></path>
                  <path
                    v-else
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M6 18L18 6M6 6l12 12"
                  ></path>
                </svg>
                <span class="font-medium">{{ option ? "True" : "False" }}</span>
              </div>
            </button>
          </div>

          <!-- Hint Button -->
          <button
            v-if="question.hint || question.answer"
            class="mt-4 text-sm text-body hover:text-primary dark:hover:text-primary-dark transition-colors flex items-center"
            @click="toggleHint(question.question)"
          >
            <svg
              class="w-4 h-4 mr-1"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              ></path>
            </svg>
            {{ showHint === question.question ? "Hide Hint" : "Show Hint" }}
          </button>

          <!-- Hint Display -->
          <div
            v-if="showHint === question.question"
            class="mt-4 p-4 bg-accent/10 dark:bg-accent-dark/10 border border-accent/20 dark:border-accent-dark/20 rounded-lg"
          >
            <div class="flex items-start">
              <svg
                class="w-5 h-5 text-accent dark:text-accent-dark mr-2 mt-0.5 flex-shrink-0"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                ></path>
              </svg>
              <div>
                <p
                  v-if="question.hint"
                  class="text-sm font-medium text-accent dark:text-accent-dark mb-1"
                >
                  Hint:
                </p>
                <p v-if="question.hint" class="text-sm text-body mb-2">
                  {{ question.hint }}
                </p>
                <p
                  class="text-sm font-medium text-accent dark:text-accent-dark mb-1"
                >
                  Correct Answer:
                </p>
                <p class="text-sm text-body">{{ question.answer }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Navigation -->
      <div class="mt-12 flex justify-center space-x-4">
        <button class="btn btn-ghost" @click="router.push('/topics')">
          <svg
            class="w-4 h-4 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M10 19l-7-7m0 0l7-7m-7 7h18"
            ></path>
          </svg>
          Back to Topics
        </button>
        <button class="btn btn-primary" @click="finishQuiz">
          <svg
            class="w-4 h-4 mr-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            ></path>
          </svg>
          Finish Quiz
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useQuizStore } from "../stores/quiz";

const router = useRouter();
const route = useRoute();
const store = useQuizStore();
const activeTab = ref("MCQ");
const showHint = ref(null);

// Function to set topic from route
const setTopicFromRoute = () => {
  const topicId = decodeURIComponent(route.params.id || "");

  if (!topicId) {
    console.warn("No topic ID provided in route");
    router.push("/topics");
    return;
  }

  if (!store.currentTopic || store.currentTopic.topic !== topicId) {
    // Try to set the topic
    const success = store.setCurrentTopic(topicId);

    // If topic wasn't found, redirect to topics
    if (!success || !store.currentTopic) {
      console.warn(`Topic "${topicId}" not found, redirecting to topics`);
      router.push("/topics");
    }
  }
};

// Set current topic when component mounts
onMounted(() => {
  setTopicFromRoute();
});

// Watch for route parameter changes
watch(
  () => route.params.id,
  () => {
    setTopicFromRoute();
  }
);

const currentTopic = computed(() => store.currentTopic);
const progress = computed(() => store.progress);
const answers = computed(() => store.answers);

const filteredQuestions = computed(() => {
  if (!currentTopic.value) return [];
  return currentTopic.value.questions.filter((q) =>
    activeTab.value === "MCQ" ? q.type === "mcq" : q.type === "tf"
  );
});

const submitAnswer = (questionId, answer) => {
  store.submitAnswer(questionId, answer);
};

const toggleHint = (questionId) => {
  showHint.value = showHint.value === questionId ? null : questionId;
};

const finishQuiz = () => {
  store.finishQuiz();
  router.push("/results");
};
</script>
