<template>
  <div class="min-h-screen py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">
        {{ currentTopic?.topic }}
      </h1>

      <!-- Tabs -->
      <div class="flex space-x-4 mb-8">
        <button
          v-for="tab in ['MCQ', 'True/False']"
          :key="tab"
          class="btn flex-1"
          :class="
            activeTab === tab ? 'btn-primary' : 'bg-gray-200 text-gray-700'
          "
          @click="activeTab = tab"
        >
          {{ tab }}
        </button>
      </div>

      <!-- Progress Bar -->
      <div class="w-full bg-gray-200 rounded-full h-2.5 mb-8">
        <div
          class="bg-primary h-2.5 rounded-full transition-all duration-300"
          :style="{ width: `${progress}%` }"
        ></div>
      </div>

      <!-- Questions -->
      <div class="space-y-8">
        <div
          v-for="(question, index) in filteredQuestions"
          :key="index"
          class="card bg-white"
        >
          <h3 class="text-lg font-semibold mb-4 text-gray-800">
            {{ question.question }}
          </h3>

          <!-- MCQ Options -->
          <div v-if="question.type === 'mcq'" class="space-y-3">
            <button
              v-for="option in question.options"
              :key="option"
              class="w-full text-left p-4 rounded-lg border transition-all"
              :class="{
                'border-primary bg-primary/10':
                  answers[question.question] === option,
                'border-gray-200 hover:border-primary':
                  answers[question.question] !== option,
              }"
              @click="submitAnswer(question.question, option)"
            >
              {{ option }}
            </button>
          </div>

          <!-- True/False Options -->
          <div v-else class="flex space-x-4">
            <button
              v-for="option in [true, false]"
              :key="option"
              class="btn flex-1"
              :class="{
                'btn-primary': answers[question.question] === option,
                'bg-gray-200 text-gray-700':
                  answers[question.question] !== option,
              }"
              @click="submitAnswer(question.question, option)"
            >
              {{ option ? "True" : "False" }}
            </button>
          </div>

          <!-- Hint Button -->
          <button
            class="mt-4 text-sm text-gray-600 hover:text-primary transition-colors"
            @click="toggleHint(question.question)"
          >
            {{ showHint === question.question ? "Hide Hint" : "Show Hint" }}
          </button>

          <!-- Hint -->
          <div
            v-if="showHint === question.question"
            class="mt-2 p-4 bg-yellow-50 rounded-lg text-sm text-yellow-800"
          >
            Correct Answer: {{ question.answer }}
          </div>
        </div>
      </div>

      <!-- Navigation -->
      <div class="flex justify-between mt-8">
        <button
          class="btn bg-gray-200 text-gray-700 hover:bg-gray-300"
          @click="router.push('/topics')"
        >
          Back to Topics
        </button>
        <button class="btn btn-primary" @click="finishQuiz">Finish Quiz</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
import { useQuizStore } from "../stores/quiz";

const router = useRouter();
const store = useQuizStore();
const activeTab = ref("MCQ");
const showHint = ref(null);

const currentTopic = computed(() => store.currentTopic);
const progress = computed(() => store.progress);
const answers = computed(() => store.answers);

const filteredQuestions = computed(() => {
  if (!currentTopic.value) return [];
  return currentTopic.value.questions.filter((q) =>
    activeTab.value === "MCQ" ? q.type === "mcq" : q.type === "tf"
  );
});

const submitAnswer = (questionId, answer) => {
  store.submitAnswer(questionId, answer);
};

const toggleHint = (questionId) => {
  showHint.value = showHint.value === questionId ? null : questionId;
};

const finishQuiz = () => {
  store.finishQuiz();
  router.push("/results");
};
</script>
