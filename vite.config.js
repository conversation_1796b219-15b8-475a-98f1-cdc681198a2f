import { fileURLToPath, URL } from "node:url";

import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import vueDevTools from "vite-plugin-vue-devtools";

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue({
      template: {
        compilerOptions: {
          // Remove comments in production
          comments: false,
        },
      },
    }),
    vueDevTools(),
  ],
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url)),
    },
  },
  build: {
    // Enable source maps for debugging in production
    sourcemap: true,
    // Optimize chunk splitting
    rollupOptions: {
      output: {
        manualChunks: {
          // Separate vendor chunks
          "vendor-vue": ["vue", "vue-router", "pinia"],
          "vendor-gsap": ["gsap"],
        },
      },
    },
    // Optimize for modern browsers
    target: "es2015",
    // Enable minification with esbuild (default)
    minify: "esbuild",
  },
  // Optimize dev server
  server: {
    host: true,
    port: 3000,
  },
  // Enable CSS code splitting
  css: {
    devSourcemap: true,
  },
});
