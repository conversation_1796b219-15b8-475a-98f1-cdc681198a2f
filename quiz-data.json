{"quiz": [{"topic": "Fundamentals & Core Concepts", "questions": [{"type": "mcq", "question": "What is the primary motivation behind parallel processing?", "options": ["Achieving lower computational power", "Increasing computational speed and performance", "Reducing the complexity of algorithms", "Minimizing memory usage"], "answer": "Increasing computational speed and performance", "source": "Final Exam, Revision Sheet, Practice Exam"}, {"type": "mcq", "question": "Which law describes the exponential growth of microprocessor speed/performance?", "options": ["<PERSON>'s Law", "<PERSON><PERSON>'s Law", "<PERSON>'s Law", "<PERSON>'s Law"], "answer": "<PERSON>'s Law", "source": "Revision Sheet, Practice Exam"}, {"type": "mcq", "question": "Which term refers to doing multiple tasks simultaneously in parallel processing?", "options": ["Parallelism", "Multithreading", "Synchronization", "Concurrency"], "answer": "Concurrency", "source": "Midterm, Practice Exam"}, {"type": "mcq", "question": "What does multithreading involve in parallel processing?", "options": ["Multiple threads of execution", "Multiple memory units", "Multiple ALUs", "Multiple cache levels"], "answer": "Multiple threads of execution", "source": "Final Exam, Revision Sheet, Practice Exam"}, {"type": "mcq", "question": "Which type of parallel processing involves multiple ALUs (Arithmetic Logic Units)?", "options": ["Control-Parallel Implementation", "Data-Parallel Implementation", "Distributed Processing", "Sequential Processing"], "answer": "Data-Parallel Implementation", "source": "Revision Sheet, Practice Exam"}, {"type": "mcq", "question": "What does data-parallel implementation involve?", "options": ["Multiple Arithmetic Logic Units (ALUs)", "A single Arithmetic Logic Unit (ALU)", "Multiple memory units", "A single memory unit"], "answer": "Multiple Arithmetic Logic Units (ALUs)", "source": "Midterm, Practice Exam"}, {"type": "mcq", "question": "What is one advantage of using distributed memory systems in parallel processing?", "options": ["Reduced communication overhead", "Increased memory access latency", "Higher power consumption", "Limited scalability"], "answer": "Reduced communication overhead", "source": "Revision Sheet, Practice Exam"}, {"type": "mcq", "question": "What is the primary goal of high-performance computing?", "options": ["To solve smaller problems efficiently", "To solve larger problems effectively", "To reduce processor clock speed", "To decrease cache memory size"], "answer": "To solve larger problems effectively", "source": "Revision Sheet, Practice Exam"}, {"type": "mcq", "question": "Which of the following is NOT a benefit of parallel processing?", "options": ["Reduced hardware complexity", "Improved performance", "Increased computational speed", "Enhanced scalability"], "answer": "Reduced hardware complexity", "source": "Midterm, Practice Exam"}, {"type": "mcq", "question": "What is the approximate speed of light?", "options": ["20 cm per nanosecond (cm/ns)", "30 cm per nanosecond (cm/ns)", "10 cm per nanosecond (cm/ns)", "40 cm per nanosecond (cm/ns)"], "answer": "30 cm per nanosecond (cm/ns)", "source": "<PERSON>term, Revision Sheet, Practice Exam"}, {"type": "mcq", "question": "What is the approximate distance light travels in one nanosecond?", "options": ["10 cm", "40 cm", "30 cm", "20 cm"], "answer": "30 cm", "source": "Midterm, Practice Exam"}, {"type": "mcq", "question": "What is the fraction of the speed of light at which signals typically travel on a wire?", "options": ["100%", "90-100%", "70-90%", "40-70%"], "answer": "40-70%", "source": "Revision Sheet, Practice Exam"}, {"type": "mcq", "question": "How does reducing distances affect the speed-of-light argument's computational limit?", "options": ["It decreases the limit proportionally", "It increases the limit proportionally", "It has no effect on the limit", "It reduces the limit exponentially"], "answer": "It increases the limit proportionally", "source": "Revision Sheet, Practice Exam"}, {"type": "mcq", "question": "What does the memory-wall challenge refer to?", "options": ["The limitation of memory bandwidth for data-intensive applications", "The high cost of memory hardware", "The inability of memory to keep up with processor speed", "The difficulty of increasing memory capacity"], "answer": "The limitation of memory bandwidth for data-intensive applications", "source": "<PERSON>term, Revision Sheet, Practice Exam"}, {"type": "mcq", "question": "What does the reliability-wall challenge refer to?", "options": ["Ensuring continuous and correct system functioning with numerous processing nodes", "Improving signal propagation speed", "Reducing energy consumption", "Increasing memory bandwidth"], "answer": "Ensuring continuous and correct system functioning with numerous processing nodes", "source": "<PERSON>term, Revision Sheet, Practice Exam"}, {"type": "mcq", "question": "Which challenge refers to the energy consumption and cooling issues in high-performance computing?", "options": ["Memory-wall challenge", "Power-wall challenge", "Reliability-wall challenge", "Speed-of-light challenge"], "answer": "Power-wall challenge", "source": "Revision Sheet, Practice Exam"}, {"type": "mcq", "question": "Which of the following is NOT a challenge in high-performance computing?", "options": ["Power-wall", "Reliability-wall", "Memory-wall", "Speed-wall"], "answer": "Speed-wall", "source": "Revision Sheet MCQ Problems, Practice Exam"}, {"type": "mcq", "question": "How does miniaturization help alleviate the speed-of-light limitation?", "options": ["By reducing the number of processing nodes", "By increasing signal propagation speed", "By reducing chip diameter", "By decreasing cache memory size"], "answer": "By increasing signal propagation speed", "source": "Revision Sheet, Practice Exam"}, {"type": "tf", "question": "<PERSON>'s Law predicts that microprocessor performance doubles roughly every 18 months.", "answer": true, "source": "Revision Sheet, Practice Exam"}, {"type": "tf", "question": "The speed of light in a vacuum is about 30 cm/ns.", "answer": true, "source": "Revision Sheet, Practice Exam"}, {"type": "tf", "question": "Electrical signals in a wire travel at the full speed of light (30 cm/ns).", "answer": false, "source": "Revision Sheet, Practice Exam"}, {"type": "tf", "question": "A processor with a 3 cm chip diameter cannot execute computations faster than 10 billion times per second due to signal propagation limits.", "answer": true, "source": "Revision Sheet, Practice Exam"}, {"type": "tf", "question": "Parallel processing was first introduced in the 21st century.", "answer": false, "source": "Revision Sheet, Practice Exam"}, {"type": "tf", "question": "High-performance computing can only improve speed, not the size or complexity of problems solved.", "answer": false, "source": "Revision Sheet, Practice Exam"}, {"type": "tf", "question": "The 'memory-wall' problem refers to the difficulty of supplying enough memory bandwidth for data-intensive applications.", "answer": true, "source": "Revision Sheet, Practice Exam"}, {"type": "tf", "question": "The 'power-wall' challenge is about the increasing energy consumption and cooling demands of high-performance systems.", "answer": true, "source": "Revision Sheet, Practice Exam"}, {"type": "tf", "question": "In a large parallel system, all processing nodes fail at the same time, creating the 'reliability-wall' challenge.", "answer": false, "source": "Revision Sheet, Practice Exam"}, {"type": "tf", "question": "High-performance computing is only beneficial for scientific simulations, not commercial applications like banking transactions.", "answer": false, "source": "Revision Sheet, Practice Exam"}, {"type": "tf", "question": "The two main types of parallel processing are data-parallel and control-parallel.", "answer": true, "source": "Revision Sheet, Practice Exam"}, {"type": "tf", "question": "The primary goal of parallel processing is to reduce the number of transistors on a chip.", "answer": false, "source": "Revision Sheet, Practice Exam"}, {"type": "tf", "question": "The speed-of-light argument suggests that processor speeds can increase indefinitely with better technology.", "answer": false, "source": "Revision Sheet, Practice Exam"}, {"type": "tf", "question": "Reducing chip size by a factor of 10 can increase computational speed limits by a factor of 10.", "answer": true, "source": "Revision Sheet, Practice Exam"}, {"type": "tf", "question": "The primary goal of high-performance computing is to solve larger problems effectively.", "answer": true, "source": "Midterm, Practice Exam"}, {"type": "tf", "question": "Distributed memory systems eliminate the need for data communication between nodes.", "answer": false, "source": "Midterm, Practice Exam"}, {"type": "tf", "question": "The power-wall challenge is unrelated to energy consumption in processing nodes.", "answer": false, "source": "Midterm, Practice Exam"}]}, {"topic": "Performance, Metrics, and Laws", "questions": [{"type": "mcq", "question": "What does FLOPS stand for in the context of performance measurement?", "options": ["Fast Linear Operations per Second", "Floating-Point Operations per Second", "Finite Loop Operations per Second", "Functional Logic Operations per Second"], "answer": "Floating-Point Operations per Second", "source": "Revision Sheet, Practice Exam"}, {"type": "mcq", "question": "What does FLOPS measure?", "options": ["Computing performance", "Signal propagation speed", "Energy efficiency", "Memory capacity"], "answer": "Computing performance", "source": "Midterm, Practice Exam"}, {"type": "mcq", "question": "What is the term used to describe the rate of executed instructions per second (IPS)?", "options": ["FLOPS", "MIPS", "DMA", "ALU"], "answer": "MIPS", "source": "Revision Sheet, Practice Exam"}, {"type": "mcq", "question": "What is the approximate performance limitation on a processor due to the speed-of-light argument?", "options": ["1 GIPS", "10 GIPS", "100 GIPS", "1000 GIPS"], "answer": "10 GIPS", "source": "Revision Sheet, Practice Exam"}, {"type": "tf", "question": "FLOPS (Floating-Point Operations Per Second) is a measure of processor performance that increases at the same rate as IPS (Instructions Per Second).", "answer": true, "source": "Revision Sheet, Practice Exam"}, {"type": "tf", "question": "Benchmark suites are used to measure processor performance but do not reflect real-world applications accurately.", "answer": false, "source": "Revision Sheet, Practice Exam"}]}, {"topic": "Parallel Architectures & Network Topologies", "questions": [{"type": "mcq", "question": "What is the primary advantage of Completely Connected Networks (CCNs)?", "options": ["Low construction cost", "Low delay complexity", "Guaranteed fast message delivery", "Scalability for large values of N"], "answer": "Guaranteed fast message delivery", "source": "Revision Sheet, Practice Exam"}, {"type": "mcq", "question": "What is the formula to calculate the number of links in a Completely Connected Network (CCN) with N nodes?", "options": ["N(N+1)/2", "N(N-1)", "N(N-1)/2", "N²"], "answer": "N(N-1)/2", "source": "Revision Sheet, Handwritten Notes, Practice Exam"}, {"type": "tf", "question": "In a CCN, the number of links is calculated as N(N-1).", "answer": false, "source": "Revision Sheet, Practice Exam"}, {"type": "tf", "question": "A CCN guarantees that messages only need to traverse one link from source to destination.", "answer": true, "source": "Revision Sheet, Practice Exam"}, {"type": "tf", "question": "The delay complexity of a CCN is O(1), meaning message transfer time does not increase with network size.", "answer": true, "source": "Revision Sheet, Practice Exam"}]}, {"topic": "Memory Systems & Cache Coherence", "questions": [{"type": "mcq", "question": "How is cache performance measured?", "options": ["Average access time", "Total memory usage", "Number of cache writes", "Cache hit ratio"], "answer": "Average access time", "source": "Final Exam, Practice Exam"}, {"type": "mcq", "question": "What is the write-through policy for cache coherence?", "options": ["Updates main memory only when data is evicted from cache", "Writes data to both cache and main memory simultaneously", "Immediately writes modified data to main memory", "Delays writing to main memory until specifically requested"], "answer": "Writes data to both cache and main memory simultaneously", "source": "Final Exam, Practice Exam"}, {"type": "mcq", "question": "What is a hardware solution for cache coherence?", "options": ["Compiler directives", "Software cache management", "Operating system synchronization", "Cache coherence protocols"], "answer": "Cache coherence protocols", "source": "Final Exam, Practice Exam"}, {"type": "mcq", "question": "What is a software solution for cache coherence?", "options": ["Memory snooping techniques", "Hardware-based snooping protocols", "Compiler-based coherence mechanisms", "Dedicated cache controller chip"], "answer": "Compiler-based coherence mechanisms", "source": "Final Exam, Practice Exam"}, {"type": "mcq", "question": "In memory hierarchy, how does speed relate to capacity?", "options": ["They are directly proportional", "They are inversely proportional", "Speed is always constant", "There is no relationship"], "answer": "They are inversely proportional", "source": "Final Exam, Practice Exam"}, {"type": "mcq", "question": "What is the level closest to the CPU in the memory hierarchy?", "options": ["Magnetic disks", "Main memory", "Optical disks", "CPU registers"], "answer": "CPU registers", "source": "Final Exam, Practice Exam"}, {"type": "mcq", "question": "What is the primary function of cache memory?", "options": ["To store long-term data", "To manage virtual memory", "To act as a buffer between CPU and main memory", "To link peripheral devices"], "answer": "To act as a buffer between CPU and main memory", "source": "Final Exam, Practice Exam"}, {"type": "mcq", "question": "What is the primary purpose of cache memory?", "options": ["To reduce memory latency", "To enhance signal propagation speed", "To increase memory capacity", "To improve energy efficiency"], "answer": "To reduce memory latency", "source": "<PERSON>term, Revision Sheet, Practice Exam"}, {"type": "mcq", "question": "What is an advantage of cache memory compared to main memory?", "options": ["Larger storage capacity", "Lower cost per unit", "Slower access time", "Faster access time"], "answer": "Faster access time", "source": "Final Exam, Practice Exam"}, {"type": "mcq", "question": "What is a disadvantage of cache memory?", "options": ["High data persistence", "Universally compatible", "Efficient for all data types", "Limited capacity"], "answer": "Limited capacity", "source": "Final Exam, Practice Exam"}, {"type": "mcq", "question": "What is the term for successfully finding data in the cache?", "options": ["<PERSON><PERSON> write", "<PERSON><PERSON> miss", "<PERSON><PERSON>", "<PERSON><PERSON> hit"], "answer": "<PERSON><PERSON> hit", "source": "Final Exam, Practice Exam"}, {"type": "mcq", "question": "What is one method mentioned to ease the speed-of-light limitation in computing?", "options": ["Increasing chip diameter", "Reducing signal transmission speed", "Implementing cache memory", "Decreasing processor clock speed"], "answer": "Implementing cache memory", "source": "Revision Sheet, Practice Exam"}]}, {"topic": "Synchronization & Concurrency", "questions": [{"type": "mcq", "question": "What is thread synchronization concerned with?", "options": ["Stopping all threads", "Ensuring safe access to shared resources by concurrent threads", "Avoiding conflicts when multiple threads modify data at once", "Speeding up individual threads"], "answer": "Ensuring safe access to shared resources by concurrent threads", "source": "Final Exam, Practice Exam"}, {"type": "mcq", "question": "Why is synchronizing threads important?", "options": ["To improve readability of code", "To prevent data competition due to conflicts", "To share data efficiently between all other threads", "To prevent race conditions where threads interfere with each other"], "answer": "To prevent race conditions where threads interfere with each other", "source": "Final Exam, Practice Exam"}, {"type": "mcq", "question": "What type of lock allows simultaneous access for many threads but exclusive write access for one thread?", "options": ["Binary semaphore", "Mutex lock", "Read-write lock", "Counting semaphore"], "answer": "Read-write lock", "source": "Final Exam, Practice Exam"}, {"type": "mcq", "question": "What is the primary function of a semaphore?", "options": ["To manage memory allocation", "To control access to critical resources", "To synchronize processes or threads", "To improve communication between threads"], "answer": "To control access to critical resources", "source": "Final Exam, Practice Exam"}, {"type": "mcq", "question": "How many possible values can a binary semaphore hold?", "options": ["Any integer value", "Unlimited values", "0 or 1", "A range of specific values"], "answer": "0 or 1", "source": "Final Exam, Practice Exam"}, {"type": "mcq", "question": "What is the other name for a binary semaphore?", "options": ["Counting semaphore", "Read-write lock", "Mutex lock", "Mutex"], "answer": "Mutex", "source": "Final Exam, Practice Exam"}, {"type": "mcq", "question": "What does a mutex lock do?", "options": ["Grants read access to multiple threads", "Allows unlimited writes from any thread", "Ensures exclusive access to a critical section", "Controls access to a pool of resources"], "answer": "Ensures exclusive access to a critical section", "source": "Final Exam, Practice Exam"}, {"type": "mcq", "question": "Which is the producer-consumer problem an example of?", "options": ["Deadlock", "Memory leak", "Infinite loop", "A synchronization problem"], "answer": "A synchronization problem", "source": "Final Exam, Practice Exam"}, {"type": "mcq", "question": "Which requirement for critical section solutions ensures no process is indefinitely blocked from entering?", "options": ["Mutual exclusion", "Livelock", "Progress", "Bounded waiting"], "answer": "Bounded waiting", "source": "Final Exam, Practice Exam"}]}, {"topic": "Hardware, System Architecture & Power", "questions": [{"type": "mcq", "question": "What is the primary benefit of pipelining in processors?", "options": ["Increased memory capacity", "Improved processor efficiency", "Reduced energy consumption", "Enhanced signal propagation speed"], "answer": "Improved processor efficiency", "source": "Revision Sheet, Practice Exam"}, {"type": "mcq", "question": "What is the role of DMA (Direct Memory Access) in parallel processing?", "options": ["To increase memory capacity", "To transfer data directly between memory and I/O devices without involving the CPU", "To reduce energy consumption", "To improve signal propagation speed"], "answer": "To transfer data directly between memory and I/O devices without involving the CPU", "source": "Revision Sheet, Practice Exam"}, {"type": "mcq", "question": "What is the relationship between pipelining and instruction cycle time?", "options": ["Pipelining decreases instruction cycle time", "Pipelining increases instruction cycle time", "Pipelining has no effect on instruction cycle time", "Pipelining increases cache memory size"], "answer": "Pipelining decreases instruction cycle time", "source": "Revision Sheet, Practice Exam"}, {"type": "mcq", "question": "What does DMA stand for in the context of parallel processing?", "options": ["Direct Memory Allocation", "Direct Memory Access", "Data Manipulation Algorithm", "Digital Memory Architecture"], "answer": "Direct Memory Access", "source": "Revision Sheet, Practice Exam"}, {"type": "mcq", "question": "Which factor contributes to the power-wall challenge in high-performance computing?", "options": ["Low processor utilization", "High energy efficiency", "Energy consumption by processing nodes", "Minimal cooling requirements"], "answer": "Energy consumption by processing nodes", "source": "Revision Sheet, Practice Exam"}, {"type": "tf", "question": "Pipelining and cache memory help overcome the speed-of-light limitation in processors.", "answer": true, "source": "Revision Sheet, Practice Exam"}]}]}