/** @type {import('tailwindcss').Config} */
export default {
  darkMode: "class",
  content: ["./index.html", "./src/**/*.{vue,js,ts,jsx,tsx}"],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: "#6366f1", // indigo-500
          dark: "#818cf8", // indigo-400
        },
        secondary: {
          DEFAULT: "#10b981", // emerald-500
          dark: "#34d399", // emerald-400
        },
        accent: {
          DEFAULT: "#f59e42", // amber-500
          dark: "#fbbf24", // amber-400
        },
        background: {
          DEFAULT: "#f8fafc", // slate-50
          dark: "#18181b", // zinc-900
        },
        surface: {
          DEFAULT: "#fff", // white
          dark: "#27272a", // zinc-800
        },
      },
      animation: {
        "bounce-slow": "bounce 3s infinite",
        "pulse-slow": "pulse 3s infinite",
      },
    },
  },
  plugins: [],
};
