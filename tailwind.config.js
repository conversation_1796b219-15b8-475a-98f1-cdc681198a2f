/** @type {import('tailwindcss').Config} */
export default {
  darkMode: "class",
  content: ["./index.html", "./src/**/*.{vue,js,ts,jsx,tsx}"],
  theme: {
    extend: {
      colors: {
        primary: {
          50: "#eef2ff",
          100: "#e0e7ff",
          200: "#c7d2fe",
          300: "#a5b4fc",
          400: "#818cf8",
          500: "#6366f1",
          600: "#4f46e5",
          700: "#4338ca",
          800: "#3730a3",
          900: "#312e81",
          DEFAULT: "#6366f1", // indigo-500
          dark: "#818cf8", // indigo-400
        },
        secondary: {
          50: "#ecfdf5",
          100: "#d1fae5",
          200: "#a7f3d0",
          300: "#6ee7b7",
          400: "#34d399",
          500: "#10b981",
          600: "#059669",
          700: "#047857",
          800: "#065f46",
          900: "#064e3b",
          DEFAULT: "#10b981", // emerald-500
          dark: "#34d399", // emerald-400
        },
        accent: {
          50: "#fffbeb",
          100: "#fef3c7",
          200: "#fde68a",
          300: "#fcd34d",
          400: "#fbbf24",
          500: "#f59e0b",
          600: "#d97706",
          700: "#b45309",
          800: "#92400e",
          900: "#78350f",
          DEFAULT: "#f59e0b", // amber-500
          dark: "#fbbf24", // amber-400
        },
        background: {
          DEFAULT: "#ffffff", // white
          secondary: "#f8fafc", // slate-50
          dark: "#0f0f23", // custom dark blue
          "dark-secondary": "#1a1a2e", // custom dark blue secondary
        },
        surface: {
          DEFAULT: "#ffffff", // white
          secondary: "#f1f5f9", // slate-100
          dark: "#16213e", // custom dark blue surface
          "dark-secondary": "#0e1628", // custom dark blue surface secondary
        },
        text: {
          primary: "#1e293b", // slate-800
          secondary: "#64748b", // slate-500
          tertiary: "#94a3b8", // slate-400
          "dark-primary": "#f1f5f9", // slate-100
          "dark-secondary": "#cbd5e1", // slate-300
          "dark-tertiary": "#94a3b8", // slate-400
        },
        border: {
          DEFAULT: "#e2e8f0", // slate-200
          secondary: "#cbd5e1", // slate-300
          dark: "#334155", // slate-700
          "dark-secondary": "#475569", // slate-600
        },
      },
      animation: {
        "bounce-slow": "bounce 3s infinite",
        "pulse-slow": "pulse 3s infinite",
        "fade-in": "fadeIn 0.3s ease-in-out",
        "slide-up": "slideUp 0.3s ease-out",
      },
      keyframes: {
        fadeIn: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        slideUp: {
          "0%": { transform: "translateY(10px)", opacity: "0" },
          "100%": { transform: "translateY(0)", opacity: "1" },
        },
      },
      boxShadow: {
        "dark-sm": "0 1px 2px 0 rgba(0, 0, 0, 0.3)",
        "dark-md":
          "0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)",
        "dark-lg":
          "0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2)",
        "dark-xl":
          "0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2)",
      },
    },
  },
  plugins: [],
};
