#!/usr/bin/env node

/**
 * Route Testing Script for Netlify Deployment
 *
 * This script tests all the routes in the Vue.js quiz application
 * to ensure they work correctly after deployment.
 *
 * Usage:
 * node scripts/test-routes.js <base-url>
 *
 * Example:
 * node scripts/test-routes.js https://your-app.netlify.app
 */

import https from "https";
import http from "http";
import { URL } from "url";

// Define all routes that should be accessible in the application
const routes = [
  "/",
  "/topics",
  "/topic/Fundamentals%20%26%20Core%20Concepts",
  "/topic/Performance%2C%20Metrics%2C%20and%20Laws",
  "/topic/Parallel%20Architectures%20%26%20Network%20Topologies",
  "/topic/Memory%20Systems%20%26%20Cache%20Coherence",
  "/results",
  "/loading",
];

// Colors for console output
const colors = {
  green: "\x1b[32m",
  red: "\x1b[31m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  reset: "\x1b[0m",
  bold: "\x1b[1m",
};

function log(message, color = "reset") {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const client = urlObj.protocol === "https:" ? https : http;

    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname + urlObj.search,
      method: "GET",
      headers: {
        "User-Agent": "Route-Tester/1.0",
      },
    };

    const req = client.request(options, (res) => {
      let data = "";

      res.on("data", (chunk) => {
        data += chunk;
      });

      res.on("end", () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data,
          url: url,
        });
      });
    });

    req.on("error", (error) => {
      reject(error);
    });

    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error("Request timeout"));
    });

    req.end();
  });
}

async function testRoute(baseUrl, route) {
  const fullUrl = baseUrl + route;

  try {
    log(`Testing: ${route}`, "blue");
    const response = await makeRequest(fullUrl);

    if (response.statusCode === 200) {
      // Check if the response contains the Vue app
      const hasVueApp = response.body.includes('<div id="app">');
      const hasTitle = response.body.includes("Parallel Processing Quiz");

      if (hasVueApp && hasTitle) {
        log(`  ✅ SUCCESS: ${route} (${response.statusCode})`, "green");
        return { route, status: "success", statusCode: response.statusCode };
      } else {
        log(
          `  ⚠️  WARNING: ${route} returned 200 but content seems incorrect`,
          "yellow"
        );
        return { route, status: "warning", statusCode: response.statusCode };
      }
    } else {
      log(`  ❌ FAILED: ${route} (${response.statusCode})`, "red");
      return { route, status: "failed", statusCode: response.statusCode };
    }
  } catch (error) {
    log(`  ❌ ERROR: ${route} - ${error.message}`, "red");
    return { route, status: "error", error: error.message };
  }
}

async function testAllRoutes(baseUrl) {
  log(`\n${colors.bold}Testing Vue.js Quiz App Routes${colors.reset}`, "blue");
  log(`Base URL: ${baseUrl}\n`);

  const results = [];

  for (const route of routes) {
    const result = await testRoute(baseUrl, route);
    results.push(result);

    // Small delay between requests to be nice to the server
    await new Promise((resolve) => setTimeout(resolve, 500));
  }

  // Summary
  log(`\n${colors.bold}Test Summary:${colors.reset}`);

  const successful = results.filter((r) => r.status === "success").length;
  const warnings = results.filter((r) => r.status === "warning").length;
  const failed = results.filter((r) => r.status === "failed").length;
  const errors = results.filter((r) => r.status === "error").length;

  log(`✅ Successful: ${successful}`, "green");
  if (warnings > 0) log(`⚠️  Warnings: ${warnings}`, "yellow");
  if (failed > 0) log(`❌ Failed: ${failed}`, "red");
  if (errors > 0) log(`💥 Errors: ${errors}`, "red");

  log(`\nTotal routes tested: ${results.length}`);

  if (failed > 0 || errors > 0) {
    log(
      "\n❌ Some routes are not working correctly. Check your Netlify redirects configuration.",
      "red"
    );
    process.exit(1);
  } else if (warnings > 0) {
    log(
      "\n⚠️  All routes are accessible but some returned unexpected content.",
      "yellow"
    );
    process.exit(0);
  } else {
    log("\n🎉 All routes are working correctly!", "green");
    process.exit(0);
  }
}

// Main execution
const baseUrl = process.argv[2];

if (!baseUrl) {
  log("Usage: node scripts/test-routes.js <base-url>", "red");
  log(
    "Example: node scripts/test-routes.js https://your-app.netlify.app",
    "yellow"
  );
  process.exit(1);
}

// Validate URL
try {
  new URL(baseUrl);
} catch (error) {
  log(`Invalid URL: ${baseUrl}`, "red");
  process.exit(1);
}

// Remove trailing slash
const cleanBaseUrl = baseUrl.replace(/\/$/, "");

testAllRoutes(cleanBaseUrl);
