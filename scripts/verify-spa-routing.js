#!/usr/bin/env node

/**
 * Local SPA Routing Verification Script
 *
 * This script verifies that the built application has proper SPA routing support
 * by checking the presence of required files and configurations.
 */

import fs from "fs";
import path from "path";

const colors = {
  green: "\x1b[32m",
  red: "\x1b[31m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  reset: "\x1b[0m",
  bold: "\x1b[1m",
};

function log(message, color = "reset") {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkFile(filePath, description) {
  if (fs.existsSync(filePath)) {
    log(`✅ ${description}`, "green");
    return true;
  } else {
    log(`❌ ${description}`, "red");
    return false;
  }
}

function checkFileContent(filePath, searchString, description) {
  try {
    const content = fs.readFileSync(filePath, "utf8");
    if (content.includes(searchString)) {
      log(`✅ ${description}`, "green");
      return true;
    } else {
      log(`❌ ${description}`, "red");
      return false;
    }
  } catch (error) {
    log(`❌ ${description} - File not readable`, "red");
    return false;
  }
}

function main() {
  log(`\n${colors.bold}SPA Routing Verification${colors.reset}`, "blue");
  log("Checking build output for proper SPA routing configuration...\n");

  let allChecksPass = true;

  // Check if dist directory exists
  if (!checkFile("dist", "Build output directory (dist) exists")) {
    log('\n❌ Build output not found. Run "npm run build" first.', "red");
    process.exit(1);
  }

  // Check essential files
  allChecksPass &= checkFile("dist/index.html", "Main HTML file exists");
  allChecksPass &= checkFile(
    "dist/_redirects",
    "Netlify redirects file exists"
  );
  allChecksPass &= checkFile(
    "netlify.toml",
    "Netlify configuration file exists"
  );

  // Check redirects file content
  allChecksPass &= checkFileContent(
    "dist/_redirects",
    "/*    /index.html   200",
    "Redirects file contains SPA routing rule"
  );

  // Check netlify.toml content
  allChecksPass &= checkFileContent(
    "netlify.toml",
    'publish = "dist"',
    "Netlify config specifies correct publish directory"
  );

  allChecksPass &= checkFileContent(
    "netlify.toml",
    'to = "/index.html"',
    "Netlify config contains SPA redirect rule"
  );

  // Check index.html content
  allChecksPass &= checkFileContent(
    "dist/index.html",
    '<div id="app">',
    "HTML file contains Vue app mount point"
  );

  allChecksPass &= checkFileContent(
    "dist/index.html",
    "Parallel Processing Quiz",
    "HTML file contains correct title"
  );

  // Check for assets
  const assetsDir = "dist/assets";
  if (fs.existsSync(assetsDir)) {
    const assets = fs.readdirSync(assetsDir);
    const hasJS = assets.some((file) => file.endsWith(".js"));
    const hasCSS = assets.some((file) => file.endsWith(".css"));

    if (hasJS) {
      log("✅ JavaScript assets found", "green");
    } else {
      log("❌ No JavaScript assets found", "red");
      allChecksPass = false;
    }

    if (hasCSS) {
      log("✅ CSS assets found", "green");
    } else {
      log("❌ No CSS assets found", "red");
      allChecksPass = false;
    }
  } else {
    log("❌ Assets directory not found", "red");
    allChecksPass = false;
  }

  // Summary
  log(`\n${colors.bold}Verification Summary:${colors.reset}`);

  if (allChecksPass) {
    log(
      "🎉 All checks passed! Your app is ready for Netlify deployment.",
      "green"
    );
    log("\nNext steps:", "blue");
    log("1. Push your code to your Git repository", "yellow");
    log("2. Connect your repository to Netlify", "yellow");
    log("3. Set build command to: npm run build", "yellow");
    log("4. Set publish directory to: dist", "yellow");
    log("5. Deploy and test your routes!", "yellow");

    log("\nTo test after deployment:", "blue");
    log("node scripts/test-routes.js https://your-app.netlify.app", "yellow");
  } else {
    log(
      "❌ Some checks failed. Please fix the issues before deploying.",
      "red"
    );
    process.exit(1);
  }
}

main();
