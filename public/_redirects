# Netlify redirects file for Vue.js SPA
# This ensures all routes are handled by the client-side router

# Handle all SPA routes - redirect everything to index.html with 200 status
# The 200 status code tells <PERSON><PERSON> to serve index.html but keep the original URL
/* /index.html 200


# Optional: Redirect old URLs or handle specific cases
# Example: Redirect old quiz URLs to new format
# /old-quiz/*  /topic/:splat  301

# Optional: Handle API routes if you have them in the future
# /api/*  https://your-api-domain.com/api/:splat  200

# Optional: Handle assets with proper caching
# /assets/*  /assets/:splat  200  Cache-Control: max-age=31536000

# Security headers (optional but recommended)
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
